<div class="space-y-6">
    <div class="text-center">
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white">
            Reporte de Asignación Conductor-Mina
        </h3>
        <p class="text-sm text-gray-600 dark:text-gray-400 mt-2">
            Generar reporte para la asignación seleccionada
        </p>
    </div>

    @if(isset($record))
        <div class="bg-gray-50 dark:bg-gray-800 rounded-lg p-4">
            <div class="grid grid-cols-2 gap-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                        Conductor
                    </label>
                    <p class="mt-1 text-sm text-gray-900 dark:text-white">
                        {{ $record->driver->full_name ?? 'N/A' }}
                    </p>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                        DNI
                    </label>
                    <p class="mt-1 text-sm text-gray-900 dark:text-white">
                        {{ $record->driver->dni ?? 'N/A' }}
                    </p>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                        Mina
                    </label>
                    <p class="mt-1 text-sm text-gray-900 dark:text-white">
                        {{ $record->mine->name ?? 'N/A' }}
                    </p>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                        Estado
                    </label>
                    <p class="mt-1 text-sm text-gray-900 dark:text-white">
                        {{ $record->status ?? 'N/A' }}
                    </p>
                </div>
            </div>
        </div>

        <div class="flex justify-center space-x-3">
            <button type="button" 
                    class="inline-flex items-center px-4 py-2 bg-red-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-red-500 focus:outline-none focus:border-red-700 focus:ring focus:ring-red-200 active:bg-red-600 disabled:opacity-25 transition">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                </svg>
                Generar PDF
            </button>
            <button type="button" 
                    class="inline-flex items-center px-4 py-2 bg-green-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-green-500 focus:outline-none focus:border-green-700 focus:ring focus:ring-green-200 active:bg-green-600 disabled:opacity-25 transition">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3M3 17V7a2 2 0 012-2h6l2 2h6a2 2 0 012 2v8a2 2 0 01-2 2H5a2 2 0 01-2-2z"></path>
                </svg>
                Exportar Excel
            </button>
        </div>
    @else
        <div class="text-center text-gray-500 dark:text-gray-400">
            <p>No se encontró información del registro.</p>
        </div>
    @endif
</div>
