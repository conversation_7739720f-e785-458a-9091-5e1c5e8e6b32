<?php

namespace App\Livewire;

use App\Models\Mine;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Filament\Forms\Form;
use Filament\Forms;
use Filament\Notifications\Notification;
use Livewire\Component;

class DriveMineAssigmentReport extends Component implements HasForms
{
    use InteractsWithForms;

    public ?array $data = [];

    public function mount(): void
    {
        $this->form->fill();
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Select::make('mine_id')
                    ->label('Mina')
                    ->options(Mine::all()->pluck('name', 'id'))
                    ->searchable()
                    ->required()
                    ->preload()
                    ->native(false),
                Forms\Components\DatePicker::make('start_date')
                    ->label('Fecha Inicio')
                    ->required()
                    ->native(false),
                Forms\Components\DatePicker::make('end_date')
                    ->label('Fecha Fin')
                    ->required()
                    ->native(false)
                    ->after('start_date'),
            ])
            ->statePath('data');
    }

    public function create(): void
    {
        $data = $this->form->getState();

        // Here you can add your report generation logic
        // For now, we'll just show a success notification

        Notification::make()
            ->title('Reporte generado exitosamente')
            ->success()
            ->send();

        // You can add logic here to generate and download the report
        // For example: return response()->download($reportPath);
    }

    public function render()
    {
        return view('livewire.drive-mine-assigment-report');
    }
}
